{"name": "expense-tracke", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/app.js", "worker": "node src/jobs/Worker.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "bull": "^4.16.5", "csv-writer": "^1.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "fast-csv": "^5.0.2", "ioredis": "^5.6.1", "jest": "^30.0.2", "joi": "^17.13.3", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "nodemailer": "^7.0.3", "pg": "^8.16.1", "redis": "^5.5.6", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}